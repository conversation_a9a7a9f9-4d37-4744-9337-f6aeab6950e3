package main

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	teammanagementclient "github.com/augmentcode/augment/services/auth/central/team_management_client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/request_context"
)

func TestStreamingIntegration(t *testing.T) {
	server, cleanup := newTestTeamManagementServer(t)
	defer cleanup()

	// Create some test user tenant mappings
	for i := 0; i < 3; i++ {
		tenantId := fmt.Sprintf("test-tenant-%d", i)
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(tenantId)
		mapping := userTenantMappingDAO.Instantiate()
		mapping.UserId = fmt.Sprintf("test-user-%d", i)
		mapping.Tenant = tenantId
		mapping.CustomerUiRoles = []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}
		_, err := userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)
	}

	// Create a client that connects to the test server
	client, err := teammanagementclient.NewTeamManagementClient(server.grpcServer.Addr().String())
	require.NoError(t, err)
	defer client.Close()

	// Create request context with proper auth
	sessionId := requestcontext.NewRandomRequestSessionId()
	requestId := requestcontext.NewRandomRequestId()
	token := "test-token" // This would normally be a real token
	requestCtx := requestcontext.New(requestId, sessionId, "test-service", token)

	// Test the streaming client method
	userTenantMappings, err := client.GetUserTenantMappings(context.Background(), requestCtx)
	require.NoError(t, err)

	// Verify we got the expected mappings
	assert.GreaterOrEqual(t, len(userTenantMappings), 3, "Should have at least 3 user tenant mappings")

	// Verify the mappings contain expected data
	for _, mapping := range userTenantMappings {
		assert.NotEmpty(t, mapping.UserId, "User ID should not be empty")
		assert.NotEmpty(t, mapping.Tenant, "Tenant should not be empty")
		assert.NotEmpty(t, mapping.CustomerUiRoles, "Customer UI roles should not be empty")
	}
}
