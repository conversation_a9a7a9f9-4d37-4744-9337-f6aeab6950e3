{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_infra import get_input_and_documents\n", "\n", "with open(\"/mnt/efs/augment/user/zhuoran/augment_qa_v3_1_ris.txt\", \"r\") as f:\n", "    request_ids = f.read().splitlines()\n", "\n", "chat_prompt_inputs = []\n", "for index, request_id in enumerate(request_ids):\n", "    print(request_id)\n", "    chat_prompt_input, _ = get_input_and_documents(request_id)\n", "    chat_prompt_inputs.append(chat_prompt_input)\n", "    print(f\"Collected {index}/{len(request_ids)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import chat_prompt_dict_from_input\n", "\n", "chat_prompt_dicts = [\n", "    chat_prompt_dict_from_input(chat_prompt_input)\n", "    for chat_prompt_input in chat_prompt_inputs\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/chat_prompt_dicts.json\", \"w\"\n", ") as f:\n", "    json.dump(chat_prompt_dicts, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from research.tools.chat_replay.replay_utils import chat_prompt_input_from_dict\n", "\n", "with open(\"/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/chat_prompt_dicts.json\") as f:\n", "    chat_prompt_dicts = json.load(f)\n", "\n", "chat_prompt_inputs = []\n", "for index, chat_prompt_dict in enumerate(chat_prompt_dicts):\n", "    print(f\"Converting {index}/{len(chat_prompt_dicts)}\")\n", "    chat_prompt_input = chat_prompt_input_from_dict(chat_prompt_dict)\n", "    chat_prompt_inputs.append(chat_prompt_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.system_prompts import (\n", "    CLAUDE_SYSTEM_PROMPT_V14,\n", "    CLAUDE_SYSTEM_PROMPT_V11,\n", "    CLAUDE_SYSTEM_PROMPT_V12,\n", ")\n", "\n", "V11 = CLAUDE_SYSTEM_PROMPT_V11\n", "V12 = CLAUDE_SYSTEM_PROMPT_V12\n", "V14 = CLAUDE_SYSTEM_PROMPT_V14.format(\n", "    model_name=\"Claude 3.7 Sonnet\", creator=\"Anthropic\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "from research.tools.chat_replay.replay_utils import TOKEN_APPORTIONMENT, TOKEN_COUNTER\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(\n", "    token_counter=TOKEN_COUNTER,\n", "    token_apportionment=TOKEN_APPORTIONMENT,\n", "    system_prompt_factory=lambda token_counter: StringFormatter(\n", "        V14, token_counter=token_counter\n", "    ),\n", "    retrieval_section_version=2,\n", ")\n", "\n", "prompt_outputs = []\n", "for index, chat_prompt_input in enumerate(chat_prompt_inputs):\n", "    print(f\"Formatting prompt {index}/{len(chat_prompt_inputs)}\")\n", "    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "    prompt_outputs.append(prompt_output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import Anthropic\n", "\n", "from research.environments import get_eng_secret\n", "\n", "anthropic_api_key = get_eng_secret(\"seal-research-anthropic-key\")\n", "anthropic_client = Anthropic(\n", "    api_key=anthropic_api_key,\n", "    max_retries=1,\n", "    timeout=300,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 1\n", "prompt_output = prompt_outputs[index]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from research.tools.chat_replay.replay_utils import get_claude_parameters\n", "\n", "\n", "def get_claude_output(prompt_output, model_name, system_prompt, print_=False):\n", "    claude_parameters = get_claude_parameters(\n", "        prompt_output, client_type=\"anthropic\", base_model_version=\"sonnet3.7\"\n", "    )\n", "    messages = claude_parameters[\"messages\"]\n", "\n", "    total_text = \"\"\n", "    for retry in range(5):\n", "        try:\n", "            with anthropic_client.messages.stream(\n", "                model=model_name,\n", "                max_tokens=8192,\n", "                temperature=1,\n", "                system=system_prompt,\n", "                messages=messages,\n", "            ) as stream:\n", "                total_text = \"\"\n", "                for chunk in stream:\n", "                    if (\n", "                        chunk.type == \"content_block_delta\"\n", "                        and chunk.delta.type == \"thinking_delta\"\n", "                    ):\n", "                        text = chunk.delta.thinking\n", "                    elif chunk.type == \"text\":\n", "                        text = chunk.text\n", "                    else:\n", "                        continue\n", "                    if print_:\n", "                        print(text, end=\"\")\n", "                    total_text += text\n", "            break\n", "        except Exception as e:\n", "            print(f\"Claude error: {e}\")\n", "            time.sleep(60 * 2**retry)\n", "    return total_text\n", "\n", "\n", "_ = get_claude_output(prompt_output, \"claude-3-5-sonnet-20241022\", V14, print_=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "import concurrent.futures\n", "\n", "\n", "def get_claude_outputs(prompt_outputs, model_name, system_prompt):\n", "    # Process results in parallel\n", "    executor = ThreadPoolExecutor(max_workers=len(prompt_outputs))\n", "    futures = []\n", "    for index, prompt_output in enumerate(prompt_outputs):\n", "        print(f\"Submitting prompt {index}/{len(prompt_outputs)}\")\n", "        future = executor.submit(\n", "            get_claude_output, prompt_output, model_name, system_prompt\n", "        )\n", "        futures.append((index, future))\n", "\n", "    # Create a list with None values to store results in order\n", "    results = [None] * len(prompt_outputs)\n", "    future_to_index = {f: i for i, f in futures}\n", "\n", "    # Collect results as they complete\n", "    completed = 0\n", "    for future in concurrent.futures.as_completed([f for _, f in futures]):\n", "        index = future_to_index[future]\n", "        text = future.result()\n", "        results[index] = text  # Store in correct position\n", "        print(\n", "            f\"Completed {completed}/{len(prompt_outputs)} (index {index}): length {len(text)}\"\n", "        )\n", "        completed += 1\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_3_5 = get_claude_outputs(prompt_outputs, \"claude-3-5-sonnet-20241022\", V14)\n", "with open(\"/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/results_3_5.json\", \"w\") as f:\n", "    json.dump(results_3_5, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["V14_R2 = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude 3.7 Sonnet model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- BE VERY BRIEF. Provide only the most relevant and actionable information. Make code blocks as short as possible by omitting unchanged parts and using placeholder comments.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "BE VERY BRIEF BY ONLY PROVIDING <10 LINES OF THE CODE. If you give correct XML structure, it will be parsed into a clickable code block, and the user can always click it to see the part in the full file.\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "````yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "````\n", "</augment_code_snippet>\n", "\n", "BE VERY BRIEF BY ONLY PROVIDING NEWLY ADDED OR MODIFIED LINES. If you give correct XML structure, it will be parsed into an appliable code block, and there will be a subsequent model that applies the changes to the user's code. Its success depends on:\n", "2.1. You outputing correct XML tags around the codeblocks.\n", "2.2. You focusing ONLY on added or modified lines, with no extra lines showing existing code.\n", "2.3. Be EXTREMELY BRIEF. The shorter the better. Use placeholders to reduce codeblock length.\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "````ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "````\n", "</augment_code_snippet>\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_3_7 = get_claude_outputs(prompt_outputs, \"claude-3-7-sonnet-20250219\", V14_R2)\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/results_3_7_r2.json\", \"w\"\n", ") as f:\n", "    json.dump(results_3_7, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def analyze_results(results):\n", "    sorted_results = sorted(results, key=lambda x: len(x))\n", "    code_block_lists = []\n", "    for result in results:\n", "        code_blocks = re.findall(\n", "            r\"<augment_code_snippet.*?>(.*?)</augment_code_snippet>\", result, re.DOTALL\n", "        )\n", "        code_block_lists.append(code_blocks)\n", "    all_code_blocks = [\n", "        code_block for code_blocks in code_block_lists for code_block in code_blocks\n", "    ]\n", "    result = {\n", "        \"Min length\": len(sorted_results[0]),\n", "        \"Max length\": len(sorted_results[-1]),\n", "        \"Avg length\": sum(len(x) for x in results) / len(results),\n", "        \"Median length\": len(sorted_results[len(results) // 2]),\n", "        \"Avg code block count\": sum(\n", "            len(code_blocks) for code_blocks in code_block_lists\n", "        )\n", "        / len(results),\n", "        \"Avg code block length\": sum(len(code_block) for code_block in all_code_blocks)\n", "        / len(all_code_blocks)\n", "        if all_code_blocks\n", "        else 0,\n", "    }\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# List all files in the directory\n", "directory = Path(\"/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/\")\n", "result_files = list(directory.glob(\"results_*.json\"))\n", "\n", "analysis_results = []\n", "for file_path in result_files:\n", "    with open(file_path, \"r\") as f:\n", "        results = json.load(f)\n", "    result = analyze_results(results)\n", "    result[\"model\"] = file_path.stem\n", "    analysis_results.append(result)\n", "\n", "df = pd.DataFrame(analysis_results)\n", "df = df.set_index(\"model\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[97]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# String replacement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.system_prompts import CLAUDE_SYSTEM_PROMPT_V14\n", "\n", "V14_REPLACE = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the {model_name} model created by {creator}.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be EXTREMELY CONCISE and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag or use the str_replace tool. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Use `str_replace` tool for edits.\n", "\n", "3. New code or text: Use `str_replace` tool with empty `old_text`.\n", "\n", "4. Note that the `str_replace` tool will be formatted as\n", "\n", "<augment_code_snippet path=\"<file_path>\" mode=\"EDIT\">\n", "````<language>\n", "<new_text>\n", "````\n", "</augment_code_snippet>\n", "\n", "without mention of the tool or `old_text`.\n", "\n", "\"\"\".format(model_name=\"Claude 3.7 Sonnet\", creator=\"Anthropic\")\n", "\n", "V14 = CLAUDE_SYSTEM_PROMPT_V14.format(\n", "    model_name=\"Claude 3.7 Sonnet\", creator=\"Anthropic\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic.types import ToolParam\n", "\n", "\n", "claude_parameters = get_claude_parameters(\n", "    prompt_output, client_type=\"anthropic\", base_model_version=\"sonnet3.7\"\n", ")\n", "messages = claude_parameters[\"messages\"]\n", "\n", "total_text = \"\"\n", "finished = False\n", "while not finished:\n", "    with anthropic_client.messages.stream(\n", "        model=\"claude-3-7-sonnet-20250219\",\n", "        # model=\"claude-3-5-sonnet-20241022\",\n", "        max_tokens=8192,\n", "        temperature=1,\n", "        system=V14_REPLACE,\n", "        messages=messages + [{\"role\": \"assistant\", \"content\": total_text.strip()}],\n", "        tools=[\n", "            ToolParam(\n", "                name=\"str_replace\",\n", "                description=\"Replace text with new text\",\n", "                input_schema={\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"old_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The original text to be replaced.\",\n", "                        },\n", "                        \"new_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text to replace with.\",\n", "                        },\n", "                        \"path\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The path to the file where the replacement should occur.\",\n", "                        },\n", "                        \"language\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The programming language of the code (used as a language hint for Markdown code blocks).\",\n", "                        },\n", "                    },\n", "                    \"required\": [\"old_text\", \"new_text\", \"path\", \"language\"],\n", "                },\n", "            ),\n", "            ToolParam(\n", "                name=\"finish_answer\",\n", "                description=\"Finish the answer. Please finish more aggresivelly when the answer seems long.\",\n", "                input_schema={\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"done\": {\n", "                            \"type\": \"boolean\",\n", "                            \"description\": \"This must always be true.\",\n", "                        },\n", "                    },\n", "                    \"required\": [],\n", "                },\n", "            ),\n", "        ],\n", "        # extra_body={\"thinking\": {\"type\": \"enabled\", \"budget_tokens\": 8000}},\n", "    ) as stream:\n", "        start_time = time.time()\n", "        first_thinking = True\n", "        first_text = True\n", "        chunks = []\n", "        for chunk in stream:\n", "            chunks.append(chunk)\n", "            if (\n", "                chunk.type == \"content_block_delta\"\n", "                and chunk.delta.type == \"thinking_delta\"\n", "            ):\n", "                text = chunk.delta.thinking\n", "                if first_thinking:\n", "                    # text = \"# Thinking\\n\\n\" + text\n", "                    first_thinking = False\n", "            elif (\n", "                chunk.type == \"content_block_delta\" and chunk.delta.type == \"text_delta\"\n", "            ):\n", "                text = chunk.delta.text\n", "                if first_text:\n", "                    # text = \"\\n\\n# Text\\n\\n\" + text\n", "                    first_text = False\n", "            elif chunk.type == \"content_block_stop\":\n", "                if (\n", "                    chunk.content_block.type == \"tool_use\"\n", "                    and chunk.content_block.name == \"str_replace\"\n", "                ):\n", "                    text = (\n", "                        f\"\\n<augment_code_snippet path={chunk.content_block.input['path']} mode=\\\"EDIT\\\">\\n\"\n", "                        f\"```{chunk.content_block.input['language']}\\n\"\n", "                        + chunk.content_block.input[\"new_text\"]\n", "                        + \"\\n```\\n\"\n", "                        + \"</augment_code_snippet>\\n\"\n", "                    )\n", "                elif (\n", "                    chunk.content_block.type == \"tool_use\"\n", "                    and chunk.content_block.name == \"finish_answer\"\n", "                ):\n", "                    finished = True\n", "            else:\n", "                text = \"\"\n", "                # print(type(chunk))\n", "            print(text, end=\"\")\n", "            total_text += text\n", "            with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "                f.write(total_text)\n", "            latency = time.time() - start_time\n", "            # if len(total_text) > 9000:\n", "            #     break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[chunk.type for chunk in chunks[-10:]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chunks[-3].content_block.input[\"new_text\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_text = \"\"\n", "start_time = time.time()\n", "first_thinking = True\n", "first_text = True\n", "for chunk in chunks:\n", "    if chunk.type == \"content_block_delta\" and chunk.delta.type == \"thinking_delta\":\n", "        text = chunk.delta.thinking\n", "        if first_thinking:\n", "            text = \"# Thinking\\n\\n\" + text\n", "            first_thinking = False\n", "    elif chunk.type == \"content_block_delta\" and chunk.delta.type == \"text_delta\":\n", "        text = chunk.delta.text\n", "        if first_text:\n", "            text = \"\\n\\n# Text\\n\\n\" + text\n", "            first_text = False\n", "    # elif (\n", "    #     chunk.type == \"content_block_delta\"\n", "    #     and chunk.delta.type == \"input_json_delta\"\n", "    # ):\n", "    #     text = chunk.delta.partial_json\n", "    # elif chunk.type == \"text\":\n", "    #     text = chunk.text\n", "    #     if first_text:\n", "    #         text = \"\\n\\n# Text\\n\\n\" + text\n", "    #         first_text = False\n", "    # elif chunk.type == \"input_json\":\n", "    #     input_json_string += chunk.partial_json\n", "    #     text = \"\"\n", "    elif chunk.type == \"content_block_stop\":\n", "        print(chunk.content_block)\n", "        if (\n", "            chunk.content_block.type == \"tool_use\"\n", "            and chunk.content_block.name == \"str_replace\"\n", "        ):\n", "            text = (\n", "                f\"\\n````{chunk.content_block.input['language']}\\n\"\n", "                + chunk.content_block.input[\"new_text\"]\n", "                + \"\\n````\\n\"\n", "            )\n", "    else:\n", "        text = \"\"\n", "        # print(type(chunk))\n", "    print(text, end=\"\")\n", "    total_text += text\n", "    with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "        f.write(total_text)\n", "    latency = time.time() - start_time\n", "    if len(total_text) > 9000:\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prompting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.system_prompts import CLAUDE_SYSTEM_PROMPT_V14\n", "\n", "V14_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the {model_name} model created by {creator}.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "````yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "````\n", "</augment_code_snippet>\n", "\n", "ONLY provide added or modified lines. There will be a separate model specialized at taking these new lines and applying them to the existing code. The apply model's success depends on:\n", "2.1. You outputing correct XML tags around the codeblocks.\n", "2.2. You focusing ONLY on added or modified lines, with no extra lines showing existing code.\n", "2.3. Be EXTREMELY BRIEF. The shorter the better. Use placeholders to reduce codeblock length.\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "````ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "````\n", "</augment_code_snippet>\n", "\n", "REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.\n", "\n", "\"\"\".format(model_name=\"Claude 3.7 Sonnet\", creator=\"Anthropic\")\n", "\n", "V14 = CLAUDE_SYSTEM_PROMPT_V14.format(\n", "    model_name=\"Claude 3.7 Sonnet\", creator=\"Anthropic\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from research.tools.chat_replay.replay_utils import get_claude_parameters\n", "\n", "claude_parameters = get_claude_parameters(\n", "    prompt_output, client_type=\"anthropic\", base_model_version=\"sonnet3.7\"\n", ")\n", "messages = claude_parameters[\"messages\"]\n", "\n", "with anthropic_client.messages.stream(\n", "    model=\"claude-3-7-sonnet-20250219\",\n", "    # model=\"claude-3-5-sonnet-20241022\",\n", "    max_tokens=8192,\n", "    temperature=1,\n", "    system=V14_PROMPT,\n", "    messages=messages,\n", "    # extra_body={\"thinking\": {\"type\": \"enabled\", \"budget_tokens\": 8000}},\n", ") as stream:\n", "    total_text = \"\"\n", "    start_time = time.time()\n", "    first_thinking = True\n", "    first_text = True\n", "    for chunk in stream:\n", "        if chunk.type == \"content_block_delta\" and chunk.delta.type == \"thinking_delta\":\n", "            text = chunk.delta.thinking\n", "            if first_thinking:\n", "                text = \"# Thinking\\n\\n\" + text\n", "                first_thinking = False\n", "        elif chunk.type == \"text\":\n", "            text = chunk.text\n", "            if first_text:\n", "                text = \"\\n\\n# Text\\n\\n\" + text\n", "                first_text = False\n", "        else:\n", "            continue\n", "        print(text, end=\"\")\n", "        total_text += text\n", "        with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "            f.write(total_text)\n", "        latency = time.time() - start_time\n", "        if len(total_text) > 9000:\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}