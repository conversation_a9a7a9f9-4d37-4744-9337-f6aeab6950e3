#!/usr/bin/env python3
"""Ray-based Vanguard to Binks conversion pipeline.

This script refactors the original vanguard_to_binks_converter.py to use the Ray
pipeline architecture established in the codebase, following the pattern from
experimental/tongfei/chatanol/gen_stage.py.

The conversion process is broken down into Ray actors that can run in distributed
or local mode for testing.
"""

import argparse
import base64
import logging
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Optional, Any, Sequence

import google.auth
from google.cloud import bigquery, storage
from google.oauth2 import service_account
from dataclasses_json import DataClassJsonMixin

# Import official Binks schemas
from experimental.tongfei.data.binks_schemas import (
    Repository,
    File,
    DocumentWithQuestionsV2,
)

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor, RayRunner

# Import dataset infrastructure
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.tenants import DatasetTenant, get_tenant
from base.datasets.gcp_creds import get_gcp_creds

# Import Vanguard utilities
from experimental.zheren.data.vanguard_data_utils import (
    get_vanguard_tenants,
    query_chat_requests,
    get_chat_request_from_gcs,
    VanguardChatRequest,
)

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """Configuration for the processing pipeline."""

    batch_size: int = 128
    queue_size: int = 10
    blob_cache_size_bytes: int = 2**30  # 1GB
    blob_cache_num_threads: int = 32
    checkpoint_cache_size_bytes: int = 2**29  # 512MB
    checkpoint_cache_num_threads: int = 16


@dataclass
class WorkspaceGroup:
    """Groups chat requests by workspace."""

    workspace_key: str
    requests: list[VanguardChatRequest] = field(default_factory=list)
    blob_ids: set[str] = field(default_factory=set)
    file_paths: dict[str, list[str]] = field(default_factory=lambda: defaultdict(list))


def _serialize_request_row(row: dict) -> dict:
    """Convert BigQuery row to JSON-serializable format."""
    serializable_row = {}
    for key, value in row.items():
        if isinstance(value, datetime):
            serializable_row[key] = value.isoformat()
        else:
            serializable_row[key] = value
    return serializable_row


@dataclass
class VanguardRequestBatch(DataClassJsonMixin):
    """Input data structure for Ray processing - a batch of request metadata."""

    tenant_name: str
    tenant_id: str
    request_rows: list[dict]  # List of BigQuery row dicts


class VanguardToRepositoryActor(AbstractRayActor[VanguardRequestBatch, Repository]):
    """Ray actor that processes Vanguard request batches into Repository objects.

    This actor encapsulates the core conversion logic from the original script,
    handling:
    1. Fetching chat requests from GCS
    2. Retrieving blob contents
    3. Grouping by workspace
    4. Creating Repository objects
    """

    def __init__(
        self,
        blob_cache_gb: float = 1.0,
        checkpoint_cache_gb: float = 0.5,
        service_account_json: Optional[str] = None,
    ):
        super().__init__(input_cls=VanguardRequestBatch, output_cls=Repository)

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        # Store cache configuration
        self.blob_cache_size_bytes = int(blob_cache_gb * 2**30)
        self.checkpoint_cache_size_bytes = int(checkpoint_cache_gb * 2**30)

        # Cache tenant objects and their caches
        self._tenant_cache = {}
        self._blob_cache = {}
        self._checkpoint_cache = {}

        logger.info(
            f"Initialized VanguardToRepositoryActor with {blob_cache_gb}GB blob cache"
        )

    def _get_or_create_caches(self, tenant_name: str):
        """Get or create caches for a tenant."""
        if tenant_name not in self._tenant_cache:
            tenant = get_tenant(tenant_name)
            self._tenant_cache[tenant_name] = tenant

            # Initialize storage client
            storage_client = storage.Client(
                project=tenant.project_id, credentials=self.credentials
            )

            # Create blob cache
            blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
            self._blob_cache[tenant_name] = GCSBlobCache(
                blob_bucket,
                tenant.blob_bucket_prefix,
                self.blob_cache_size_bytes,
                num_threads=32,
            )

            # Create checkpoint cache
            checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
            self._checkpoint_cache[tenant_name] = GCSCheckpointCache(
                checkpoint_bucket,
                tenant.checkpoint_bucket_prefix,
                self.checkpoint_cache_size_bytes,
                num_threads=16,
            )

        return (
            self._tenant_cache[tenant_name],
            self._blob_cache[tenant_name],
            self._checkpoint_cache[tenant_name],
        )

    def process(self, row: VanguardRequestBatch) -> list[Repository]:
        """Process a batch of Vanguard requests into Repository objects."""
        tenant, blob_cache, _ = self._get_or_create_caches(row.tenant_name)

        # Fetch chat requests from GCS
        chat_requests = self._fetch_chat_requests(tenant, row.request_rows)

        if not chat_requests:
            logger.warning(
                f"No valid chat requests found in batch for {row.tenant_name}"
            )
            return []

        # Group by workspace
        workspace_groups = self._group_by_workspace(chat_requests, blob_cache)

        # Create repositories
        repositories = self._create_repositories(workspace_groups, blob_cache, tenant)

        logger.info(
            f"Processed {len(row.request_rows)} requests -> "
            f"{len(chat_requests)} valid requests -> "
            f"{len(repositories)} repositories"
        )

        return repositories

    def _fetch_chat_requests(
        self, tenant: DatasetTenant, request_rows: Sequence[dict]
    ) -> list[VanguardChatRequest]:
        """Fetch chat request details from GCS."""
        chat_requests = []

        for row in request_rows:
            request = get_chat_request_from_gcs(
                tenant, row["tenant_id"], row["request_id"], self.credentials
            )
            if request and request.blob_names:
                chat_requests.append(request)

        return chat_requests

    def _transform_blob_id(self, base64_blob_id: str) -> str:
        """Transform base64-encoded blob ID to hex format for GCS lookup."""
        try:
            blob_bytes = base64.b64decode(base64_blob_id)
            return blob_bytes.hex()
        except Exception as e:
            logger.warning(f"Failed to transform blob ID {base64_blob_id}: {e}")
            return base64_blob_id

    def _group_by_workspace(
        self, requests: Sequence[VanguardChatRequest], blob_cache: GCSBlobCache
    ) -> list[WorkspaceGroup]:
        """Group requests by workspace based on blob paths."""

        # Collect all blob IDs and transform them
        all_blob_ids = set()
        blob_id_mapping = {}

        for request in requests:
            for blob_id in request.blob_names:
                hex_id = self._transform_blob_id(blob_id)
                blob_id_mapping[blob_id] = hex_id
                all_blob_ids.add(hex_id)

        # Fetch blob paths
        blob_paths = {}
        hex_blob_ids = list(all_blob_ids)
        blob_results = blob_cache.get(hex_blob_ids)

        for hex_id, result in zip(hex_blob_ids, blob_results):
            if result:
                blob_paths[hex_id] = str(result.path)

        # Group by workspace
        workspace_groups = {}

        for request in requests:
            request_paths = []
            for blob_id in request.blob_names:
                hex_id = blob_id_mapping.get(blob_id)
                if hex_id and hex_id in blob_paths:
                    request_paths.append(blob_paths[hex_id])

            if not request_paths:
                continue

            workspace_key = self._find_workspace_key(request_paths)

            if workspace_key not in workspace_groups:
                workspace_groups[workspace_key] = WorkspaceGroup(
                    workspace_key=workspace_key
                )

            group = workspace_groups[workspace_key]
            group.requests.append(request)
            group.blob_ids.update(request.blob_names)
            group.file_paths[request.request_id] = request_paths

        return list(workspace_groups.values())

    def _find_workspace_key(self, paths: list[str]) -> str:
        """Find common workspace key from paths."""
        if len(paths) == 1:
            return str(Path(paths[0]).parent)

        # Find common prefix
        common_parts = Path(paths[0]).parts
        for path in paths[1:]:
            path_parts = Path(path).parts
            common_parts = common_parts[: min(len(common_parts), len(path_parts))]
            for i, (a, b) in enumerate(zip(common_parts, path_parts)):
                if a != b:
                    common_parts = common_parts[:i]
                    break

        return str(Path(*common_parts)) if common_parts else "root"

    def _create_repositories(
        self,
        workspace_groups: Sequence[WorkspaceGroup],
        blob_cache: GCSBlobCache,
        tenant: DatasetTenant,
    ) -> list[Repository]:
        """Create Repository objects from workspace groups."""
        repositories = []

        for group in workspace_groups:
            # Transform blob IDs
            hex_blob_ids = []
            original_to_hex = {}
            for blob_id in group.blob_ids:
                hex_id = self._transform_blob_id(blob_id)
                hex_blob_ids.append(hex_id)
                original_to_hex[blob_id] = hex_id

            # Fetch blob contents
            blob_contents = {}
            blob_results = blob_cache.get(hex_blob_ids)

            for hex_id, result in zip(hex_blob_ids, blob_results):
                if result:
                    # Find original blob ID
                    original_id = None
                    for orig, hx in original_to_hex.items():
                        if hx == hex_id:
                            original_id = orig
                            break

                    if original_id:
                        blob_contents[original_id] = {
                            "blob_id": hex_id,
                            "path": str(result.path),
                            "content": result.content,
                            "size": len(result.content.encode("utf-8")),
                        }

            if not blob_contents:
                continue

            # Create repository
            repo = self._create_repository_from_group(group, blob_contents, tenant.name)
            if repo and repo.file_list and repo.documents_with_questions:
                repositories.append(repo)

        return repositories

    def _create_repository_from_group(
        self, group: WorkspaceGroup, blob_contents: dict[str, dict], tenant_name: str
    ) -> Optional[Repository]:
        """Create a Repository object from a workspace group."""

        repo_name = f"vanguard_{tenant_name}_{group.workspace_key.replace('/', '_')}"
        repo_uuid = str(uuid.uuid4())

        # Create File objects
        file_list = []
        lang_counts = defaultdict(int)
        lang_sizes = defaultdict(int)
        total_size = 0

        for blob_id, blob_data in blob_contents.items():
            file_obj = self._create_file_object(blob_data, repo_name)
            file_list.append(file_obj)

            lang = file_obj.langpart
            lang_counts[lang] += 1
            lang_sizes[lang] += file_obj.size
            total_size += file_obj.size

        # Create DocumentWithQuestionsV2 objects
        documents_with_questions = []
        for request in group.requests:
            paths = group.file_paths.get(request.request_id, [])
            valid_paths = [
                p
                for p in paths
                if any(
                    blob_contents.get(bid, {}).get("path") == p
                    for bid in request.blob_names
                )
            ]

            if valid_paths:
                doc = DocumentWithQuestionsV2(
                    question=request.message,
                    paths=valid_paths,
                    answer="",
                )
                documents_with_questions.append(doc)

        if not file_list or not documents_with_questions:
            return None

        # Find max language stats
        if lang_counts:
            max_file_lang_name, max_file_count = max(
                lang_counts.items(), key=lambda x: x[1]
            )
        else:
            max_file_lang_name, max_file_count = "unknown", 0

        if lang_sizes:
            max_size_lang_name, max_size_total = max(
                lang_sizes.items(), key=lambda x: x[1]
            )
        else:
            max_size_lang_name, max_size_total = "unknown", 0

        return Repository(
            max_stars_repo_name=repo_name,
            max_file_lang={
                "file_count": max_file_count,
                "langpart": max_file_lang_name,
            },
            max_size_lang={
                "total_size": max_size_total,
                "langpart": max_size_lang_name,
            },
            total_size=total_size,
            file_list=file_list,
            documents_with_questions=documents_with_questions,
            repo_uuid=repo_uuid,
        )

    def _create_file_object(self, blob_data: dict, repo_name: str) -> File:
        """Create a File object from blob data."""
        path = blob_data["path"]
        content = blob_data["content"]

        # Calculate file statistics
        lines = content.split("\n")
        line_lengths = [len(line) for line in lines if line]

        avg_line_length = sum(line_lengths) / len(line_lengths) if line_lengths else 0
        max_line_length = max(line_lengths) if line_lengths else 0

        # Calculate alphanum fraction
        alphanum_chars = sum(1 for c in content if c.isalnum())
        total_chars = len(content)
        alphanum_fraction = alphanum_chars / total_chars if total_chars > 0 else 0

        # Determine file extension and language
        ext = Path(path).suffix.lower() if Path(path).suffix else None
        lang = self._guess_language(path)

        return File(
            hexsha=blob_data["blob_id"],
            size=blob_data["size"],
            ext=ext,
            max_stars_repo_path=path,
            max_stars_repo_name=repo_name,
            max_stars_repo_licenses=[],
            max_stars_count=None,
            max_stars_repo_stars_event_min_datetime=None,
            max_stars_repo_stars_event_max_datetime=None,
            max_issues_repo_path=path,
            max_issues_repo_name=repo_name,
            max_issues_repo_licenses=[],
            max_issues_count=None,
            max_issues_repo_issues_event_min_datetime=None,
            max_issues_repo_issues_event_max_datetime=None,
            max_forks_repo_path=path,
            max_forks_repo_name=repo_name,
            max_forks_repo_licenses=[],
            max_forks_count=None,
            max_forks_repo_forks_event_min_datetime=None,
            max_forks_repo_forks_event_max_datetime=None,
            content=content,
            avg_line_length=avg_line_length,
            max_line_length=max_line_length,
            alphanum_fraction=alphanum_fraction,
            langpart=lang,
        )

    def _guess_language(self, path: str) -> str:
        """Guess programming language from file path."""
        ext_to_lang = {
            ".py": "python",
            ".js": "javascript",
            ".ts": "typescript",
            ".java": "java",
            ".cpp": "cpp",
            ".cc": "cpp",
            ".c": "c",
            ".h": "c",
            ".hpp": "cpp",
            ".cs": "csharp",
            ".go": "go",
            ".rs": "rust",
            ".rb": "ruby",
            ".php": "php",
            ".swift": "swift",
            ".kt": "kotlin",
            ".scala": "scala",
            ".r": "r",
            ".sh": "bash",
            ".sql": "sql",
            ".html": "html",
            ".css": "css",
            ".json": "json",
            ".yaml": "yaml",
            ".yml": "yaml",
            ".md": "markdown",
            ".mdx": "markdown",
            ".txt": "text",
            ".xml": "xml",
            ".jsx": "javascript",
            ".tsx": "typescript",
            ".vue": "javascript",
            ".svelte": "javascript",
            ".scss": "css",
            ".sass": "css",
            ".less": "css",
            ".toml": "toml",
            ".ini": "ini",
            ".cfg": "ini",
            ".conf": "ini",
            ".dockerfile": "dockerfile",
            ".makefile": "makefile",
            ".cmake": "cmake",
            ".gradle": "gradle",
            ".pom": "xml",
            ".ipynb": "jupyter",
            ".R": "r",
            ".m": "matlab",
            ".tex": "latex",
            ".rst": "rst",
            ".adoc": "asciidoc",
            ".pod": "perl",
            ".pl": "perl",
            ".pm": "perl",
            ".lua": "lua",
            ".dart": "dart",
            ".elm": "elm",
            ".clj": "clojure",
            ".cljs": "clojure",
            ".ex": "elixir",
            ".exs": "elixir",
            ".erl": "erlang",
            ".hrl": "erlang",
            ".fs": "fsharp",
            ".fsx": "fsharp",
            ".ml": "ocaml",
            ".mli": "ocaml",
            ".pas": "pascal",
            ".pp": "pascal",
            ".nim": "nim",
            ".nims": "nim",
            ".zig": "zig",
            ".v": "verilog",
            ".vhd": "vhdl",
            ".vhdl": "vhdl",
            ".proto": "protobuf",
            ".thrift": "thrift",
            ".graphql": "graphql",
            ".gql": "graphql",
        }

        ext = Path(path).suffix.lower()
        return ext_to_lang.get(ext, "unknown")


def create_request_batches(
    tenants: list[str],
    date_from: str,
    date_to: str,
    limit: Optional[int],
    batch_size: int,
    credentials: Any,
) -> list[VanguardRequestBatch]:
    """Create batches of requests for Ray processing."""
    batches = []

    for tenant_name in tenants:
        try:
            tenant = get_tenant(tenant_name)

            # Query for request IDs
            request_rows = query_chat_requests(
                tenant, date_from, date_to, limit, credentials
            )

            if not request_rows:
                logger.warning(f"No requests found for tenant {tenant_name}")
                continue

            logger.info(f"Found {len(request_rows)} requests for tenant {tenant_name}")

            # Create batches
            for i in range(0, len(request_rows), batch_size):
                batch_rows = request_rows[i : i + batch_size]
                # Convert datetime objects to serializable format
                serializable_rows = [_serialize_request_row(row) for row in batch_rows]
                batch = VanguardRequestBatch(
                    tenant_name=tenant_name,
                    tenant_id=tenant.tenant_id,
                    request_rows=serializable_rows,
                )
                batches.append(batch)

        except Exception as e:
            logger.error(f"Error processing tenant {tenant_name}: {e}")
            continue

    return batches


def main():
    """Main function to orchestrate the Ray-based conversion process."""
    parser = argparse.ArgumentParser(
        description="Ray-based Vanguard to Binks dataset conversion"
    )

    # Data selection arguments
    parser.add_argument(
        "--date-from",
        type=str,
        required=True,
        help="Start date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--date-to",
        type=str,
        required=True,
        help="End date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--output-path", type=str, required=True, help="Output path for the JSONL file"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit number of requests per tenant (for testing)",
    )
    parser.add_argument(
        "--tenants",
        type=str,
        default=None,
        help="Comma-separated list of specific tenants to process",
    )

    # Processing configuration
    parser.add_argument(
        "--batch-size",
        type=int,
        default=128,
        help="Batch size for processing (default: 128)",
    )
    parser.add_argument(
        "--blob-cache-gb",
        type=float,
        default=1.0,
        help="Blob cache size in GB (default: 1.0)",
    )
    parser.add_argument(
        "--checkpoint-cache-gb",
        type=float,
        default=0.5,
        help="Checkpoint cache size in GB (default: 0.5)",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=1,
        help="Number of Ray workers (default: 1)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=8,
        help="Number of CPUs per worker (default: 8)",
    )
    parser.add_argument(
        "--num-gpu-per-worker",
        type=int,
        default=0,
        help="Number of GPUs per worker (default: 0)",
    )

    args = parser.parse_args()

    # Validate dates
    try:
        datetime.strptime(args.date_from, "%Y-%m-%d")
        datetime.strptime(args.date_to, "%Y-%m-%d")
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        return 1

    # Set up credentials
    credentials, _ = get_gcp_creds(args.service_account_file)

    # Get tenants to process
    if args.tenants:
        tenant_names = [t.strip() for t in args.tenants.split(",")]
    else:
        tenant_names = [t.name for t in get_vanguard_tenants()]

    logger.info(f"Processing {len(tenant_names)} tenants in {args.mode} mode")

    # Create request batches
    batches = create_request_batches(
        tenant_names,
        args.date_from,
        args.date_to,
        args.limit,
        args.batch_size,
        credentials,
    )

    if not batches:
        logger.error("No batches created - no data to process")
        return 1

    logger.info(f"Created {len(batches)} batches for processing")

    # Create temporary JSONL file with batches
    import tempfile

    with tempfile.NamedTemporaryFile(mode="w", suffix=".jsonl", delete=False) as f:
        temp_input_path = f.name
        for batch in batches:
            f.write(batch.to_json() + "\n")

    try:
        # Run Ray pipeline
        with RayRunner(
            actor_cls=VanguardToRepositoryActor,
            actor_args={
                "blob_cache_gb": args.blob_cache_gb,
                "checkpoint_cache_gb": args.checkpoint_cache_gb,
                "service_account_json": args.service_account_file,
            },
            num_workers=args.num_workers,
            num_cpu_per_worker=args.num_cpu_per_worker,
            num_gpu_per_worker=args.num_gpu_per_worker,
            local=args.mode == "local",
        ) as runner:
            runner.process_jsonl(temp_input_path, args.output_path)

        logger.info(f"Conversion complete! Output written to {args.output_path}")

        # Count results
        if args.mode == "local":
            output_file = Path(args.output_path) / Path(temp_input_path).name
            if output_file.exists():
                with open(output_file, "r") as f:
                    repo_count = sum(1 for _ in f)
                logger.info(f"Generated {repo_count} repositories")

        return 0

    finally:
        # Clean up temporary file
        Path(temp_input_path).unlink(missing_ok=True)


if __name__ == "__main__":
    exit(main())
